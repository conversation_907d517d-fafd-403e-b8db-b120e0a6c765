import discord
from discord.ext import commands, tasks
import asyncio
from datetime import datetime, timedelta, timezone
import random

TICKET_ROLE_ID = 1402926443800821761

class Ticket(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.panel_message_id = None
        self.active_tickets = {}
        self.ticket_last_activity = {}
        self.ticket_warnings_sent = set()  # thread_ids that have received warnings
        self.check_inactive_tickets.start()

    @commands.command()
    @commands.has_permissions(administrator=True)
    async def ticketpanel(self, ctx):
        """Send the ticket panel."""
        embed = discord.Embed(
            title="Contact Support",
            description="Select an option below to open a private ticket with our support team.",
            color=self.bot.mode.EMBED_COLOR
        )

        embed.add_field(
            name="Available Support Options:",
            value="- Buy - Purchase inquiries and billing support\n- Code Support - Technical assistance and troubleshooting\n- Talk to Owner - Direct communication with management",
            inline=False
        )

        embed.add_field(
            name="Important",
            value="**Please select the most appropriate option for your inquiry to ensure faster response times.**",
            inline=False
        )

        embed.set_footer(text="Click the button below to open a ticket.")
        embed.set_thumbnail(url=self.bot.user.display_avatar.url)

        view = TicketPanelView(self)
        msg = await ctx.send(embed=embed, view=view)
        self.panel_message_id = msg.id

    @commands.command()
    @commands.has_permissions(manage_messages=True)
    async def close(self, ctx):
        """Close a ticket thread."""
        if not isinstance(ctx.channel, discord.Thread):
            return await ctx.send("This command can only be used in ticket threads.")

        if not (ctx.channel.name.startswith("ticket-") or
                ctx.channel.name.startswith("[Resolved]") or
                ctx.channel.name.startswith("[Auto-Closed]")):
            return await ctx.send("This doesn't appear to be a ticket thread.")

        # Only lock the thread (don't archive) so moderators can still see it
        await ctx.channel.edit(locked=True)

        # Update channel name to show it's resolved
        try:
            # Remove existing prefixes if any
            clean_name = ctx.channel.name
            if clean_name.startswith("[Resolved] "):
                clean_name = clean_name[11:]
            elif clean_name.startswith("[Auto-Closed] "):
                clean_name = clean_name[14:]

            new_name = f"[Resolved] {clean_name}"
            await ctx.channel.edit(name=new_name)
        except:
            pass

        # Remove from active tickets and activity tracking
        user_id = None
        for uid, thread_id in list(self.active_tickets.items()):
            if thread_id == ctx.channel.id:
                user_id = uid
                break

        if user_id:
            del self.active_tickets[user_id]

        if ctx.channel.id in self.ticket_last_activity:
            del self.ticket_last_activity[ctx.channel.id]

        embed = discord.Embed(
            description=f"This ticket has been locked by {ctx.author.mention}. Moderators can still see this thread and delete it if needed. Still need help? Create another ticket in the main channel.",
            color=self.bot.mode.EMBED_COLOR
        )
        await ctx.send(embed=embed)

    @commands.command(name="open", aliases=["reopen"])
    @commands.has_permissions(manage_messages=True)
    async def open_ticket(self, ctx):
        """Unlock/reopen a resolved ticket thread."""
        if not isinstance(ctx.channel, discord.Thread):
            return await ctx.send("This command can only be used in ticket threads.")

        if not (ctx.channel.name.startswith("[Resolved]") or
                ctx.channel.name.startswith("[Auto-Closed]") or
                ctx.channel.name.startswith("ticket-")):
            return await ctx.send("This doesn't appear to be a ticket thread.")

        # Unlock the thread
        await ctx.channel.edit(locked=False)

        # Update channel name to remove resolved/auto-closed prefix
        try:
            clean_name = ctx.channel.name
            if clean_name.startswith("[Resolved] "):
                clean_name = clean_name[11:]
            elif clean_name.startswith("[Auto-Closed] "):
                clean_name = clean_name[14:]

            await ctx.channel.edit(name=clean_name)
        except:
            pass

        # Re-add to activity tracking
        self.ticket_last_activity[ctx.channel.id] = datetime.now(timezone.utc)

        embed = discord.Embed(
            description=f"This ticket has been reopened by {ctx.author.mention}. The support team can continue assisting you here.",
            color=self.bot.mode.EMBED_COLOR
        )
        await ctx.send(embed=embed)

    @commands.command(name="deleteticket", aliases=["dt"])
    @commands.has_permissions(manage_messages=True)
    async def delete_ticket(self, ctx):
        """Delete a ticket thread."""
        if not isinstance(ctx.channel, discord.Thread):
            return await ctx.send("This command can only be used in ticket threads.")

        if not (ctx.channel.name.startswith("ticket-") or
                ctx.channel.name.startswith("[Resolved]") or
                ctx.channel.name.startswith("[Auto-Closed]")):
            return await ctx.send("This doesn't appear to be a ticket thread.")

        # Remove from active tickets and activity tracking
        user_id = None
        for uid, thread_id in list(self.active_tickets.items()):
            if thread_id == ctx.channel.id:
                user_id = uid
                break

        if user_id:
            del self.active_tickets[user_id]

        if ctx.channel.id in self.ticket_last_activity:
            del self.ticket_last_activity[ctx.channel.id]

        # Send confirmation message before deletion
        embed = discord.Embed(
            description="This ticket thread will be deleted in 5 seconds...",
            color=self.bot.mode.EMBED_COLOR
        )
        await ctx.send(embed=embed)

        # Wait 5 seconds then delete
        await asyncio.sleep(5)
        try:
            await ctx.channel.delete()
        except discord.NotFound:
            # Thread was already deleted
            pass
        except discord.Forbidden:
            await ctx.send("I don't have permission to delete this thread.")
        except Exception as e:
            await ctx.send(f"Failed to delete thread: {str(e)}")

    @tasks.loop(minutes=1)  # Check every minute
    async def check_inactive_tickets(self):
        """Check for inactive tickets and send warnings or auto-close them."""
        current_time = datetime.now(timezone.utc)

        if len(self.ticket_last_activity) > 0:
            print(f"[TICKET] Checking {len(self.ticket_last_activity)} tickets at {current_time.strftime('%H:%M:%S')}")

        for thread_id, last_activity in list(self.ticket_last_activity.items()):
            try:
                thread = self.bot.get_channel(thread_id)
                if not thread or not isinstance(thread, discord.Thread):
                    # Thread doesn't exist anymore, clean up
                    del self.ticket_last_activity[thread_id]
                    continue

                if not thread.name.startswith("ticket-"):
                    continue

                time_since_activity = current_time - last_activity
                minutes_inactive = time_since_activity.total_seconds() / 60

                print(f"[TICKET] {thread.name}: {minutes_inactive:.1f} minutes inactive")

                # Send warning after 1 minute of inactivity (testing)
                if time_since_activity >= timedelta(minutes=1) and time_since_activity < timedelta(minutes=2) and thread_id not in self.ticket_warnings_sent:
                    print(f"[TICKET] Sending warning for {thread.name}")
                    # Get the ticket creator
                    ticket_creator = None
                    for user_id, t_id in self.active_tickets.items():
                        if t_id == thread_id:
                            ticket_creator = self.bot.get_user(user_id)
                            break

                    if ticket_creator:
                        # Send ping message first
                        await thread.send(f"{ticket_creator.mention}")

                        # Then send the embed
                        embed = discord.Embed(
                            description=f"hello! - do you still need help?\n\n- i'm poking because it's been over 1 minute without a response from you\n\n- i'm going to close this thread for now: if you still need help, send a message here !\n\n• this thread will automatically lock in 2 minutes",
                            color=self.bot.mode.EMBED_COLOR
                        )
                        await thread.send(embed=embed)
                        self.ticket_warnings_sent.add(thread_id)  # Mark warning as sent
                        print(f"[TICKET] Warning sent to {ticket_creator}")
                    else:
                        print(f"[TICKET] Could not find ticket creator for {thread.name}")

                # Auto-close after 2 minutes of inactivity (testing)
                elif time_since_activity >= timedelta(minutes=2):
                    print(f"[TICKET] Auto-closing {thread.name} after 2 minutes")
                    # Only lock the thread (don't archive) so moderators can still see it
                    await thread.edit(locked=True)

                    # Update channel name to show it's auto-closed
                    try:
                        new_name = f"[Auto-Closed] {thread.name}"
                        await thread.edit(name=new_name)
                    except:
                        pass

                    # Remove from active tickets and activity tracking
                    user_id = None
                    for uid, t_id in list(self.active_tickets.items()):
                        if t_id == thread_id:
                            user_id = uid
                            break

                    if user_id:
                        del self.active_tickets[user_id]

                    del self.ticket_last_activity[thread_id]

                    embed = discord.Embed(
                        description="This ticket has been automatically locked by the system due to 2 minutes of inactivity. Create a new ticket if you still need help.",
                        color=self.bot.mode.EMBED_COLOR
                    )
                    await thread.send(embed=embed)

            except Exception as e:
                print(f"[TICKET] Error checking ticket {thread_id}: {e}")
                # Clean up problematic entries
                if thread_id in self.ticket_last_activity:
                    del self.ticket_last_activity[thread_id]

    @check_inactive_tickets.before_loop
    async def before_check_inactive_tickets(self):
        await self.bot.wait_until_ready()

    def cog_unload(self):
        self.check_inactive_tickets.cancel()

    @commands.Cog.listener()
    async def on_message(self, message):
        """Track message activity in ticket threads."""
        if isinstance(message.channel, discord.Thread) and message.channel.name.startswith("ticket-"):
            # Only update last activity time if the message is from the ticket creator
            # Find the ticket creator for this thread
            ticket_creator_id = None
            for user_id, thread_id in self.active_tickets.items():
                if thread_id == message.channel.id:
                    ticket_creator_id = user_id
                    break

            # Only update activity if the message is from the ticket creator
            if ticket_creator_id and message.author.id == ticket_creator_id:
                self.ticket_last_activity[message.channel.id] = datetime.now(timezone.utc)

class TicketPanelView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=None)
        self.cog = cog

    @discord.ui.select(
        placeholder="Select a support option...",
        min_values=1,
        max_values=1,
        options=[
            discord.SelectOption(
                label="Buy",
                description="Purchase inquiries and billing support",
                value="buy"
            ),
            discord.SelectOption(
                label="Code Support",
                description="Technical assistance and troubleshooting",
                value="code_support"
            ),
            discord.SelectOption(
                label="Talk to Owner",
                description="Direct communication with management",
                value="talk_to_owner"
            )
        ],
        custom_id="ticket_select"
    )
    async def create_ticket(self, interaction: discord.Interaction, select: discord.ui.Select):
        # Respond to interaction immediately to prevent timeout
        await interaction.response.defer(ephemeral=True)

        # Get the selected option
        selected_option = select.values[0]

        # Check if user already has an active ticket
        if interaction.user.id in self.cog.active_tickets:
            existing_thread_id = self.cog.active_tickets[interaction.user.id]
            try:
                existing_thread = interaction.guild.get_thread(existing_thread_id)
                if existing_thread and not existing_thread.archived:
                    return await interaction.followup.send(
                        f"You already have an active ticket: {existing_thread.mention}",
                        ephemeral=True
                    )
                else:
                    # Clean up old reference
                    del self.cog.active_tickets[interaction.user.id]
            except:
                # Thread doesn't exist anymore, clean up
                del self.cog.active_tickets[interaction.user.id]

        guild = interaction.guild
        mod_role = guild.get_role(TICKET_ROLE_ID)

        # Generate unique ticket ID and create thread name based on selection
        ticket_id = random.randint(1000, 9999)
        thread_name = f"ticket-{selected_option}-{ticket_id}"

        try:
            # Check if bot has required permissions
            bot_permissions = interaction.channel.permissions_for(guild.me)
            if not bot_permissions.create_private_threads:
                return await interaction.followup.send(
                    "I don't have permission to create private threads in this channel.",
                    ephemeral=True
                )

            # Create a private thread
            thread = await interaction.channel.create_thread(
                name=thread_name,
                type=discord.ChannelType.private_thread,
                auto_archive_duration=4320,  # 3 days
                reason=f"Ticket created by {interaction.user}"
            )

            # Store the active ticket and initialize activity tracking
            self.cog.active_tickets[interaction.user.id] = thread.id
            self.cog.ticket_last_activity[thread.id] = datetime.now(timezone.utc)

            # Send initial message in thread with user mention and role ping
            if mod_role:
                # Send the ping message with allowed mentions to ensure the role actually pings
                await thread.send(
                    f"{interaction.user.mention} · {mod_role.mention}",
                    allowed_mentions=discord.AllowedMentions(roles=True, users=True)
                )
            else:
                await thread.send(f"{interaction.user.mention}")

            # Send the main ticket message with new format
            ticket_embed = discord.Embed(
                description="- our support team have been notified to come help !\n- whilst you are waiting, please write any additional information, such as:\n- any screenshots relating to your issue\n- any commands you ran, if applicable\n\n> thank you for your patience!",
                color=self.cog.bot.mode.EMBED_COLOR
            )
            ticket_embed.set_footer(
                text="please avoid pinging roles or staff members: we will come to help you as soon as we can.",
                icon_url=self.cog.bot.user.display_avatar.url
            )
            # Add close button
            close_view = TicketCloseView(self.cog)
            await thread.send(embed=ticket_embed, view=close_view)

            # Notify moderators with ticket info
            if mod_role:
                mod_embed = discord.Embed(
                    description=f"**{interaction.user.mention}** has created a {selected_option.replace('_', ' ').title()} support ticket.",
                    color=self.cog.bot.mode.EMBED_COLOR,
                    timestamp=datetime.now(timezone.utc)
                )
                mod_embed.add_field(
                    name="User ID",
                    value=f"`{interaction.user.id}`",
                    inline=False
                )
                await thread.send(embed=mod_embed)

            # Send success response
            success_embed = discord.Embed(
                title=f"Ticket opened!",
                description=f"Your ticket has been created at {thread.mention}. A moderator will assist you shortly.",
                color=self.cog.bot.mode.EMBED_COLOR,
            )
            await interaction.followup.send(embed=success_embed, ephemeral=True)

        except discord.Forbidden:
            await interaction.followup.send(
                "I don't have permission to create threads in this channel.",
                ephemeral=True
            )
        except discord.HTTPException as e:
            await interaction.followup.send(
                f"Failed to create ticket: {str(e)}",
                ephemeral=True
            )

class TicketCloseView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=None)
        self.cog = cog

    @discord.ui.button(label="close thread", style=discord.ButtonStyle.danger, custom_id="close_ticket")
    async def close_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Check if thread is already archived
        if interaction.channel.archived:
            return await interaction.response.send_message(
                "This ticket is already closed.",
                ephemeral=True
            )

        # Only allow the ticket creator or moderators to close
        is_ticket_creator = interaction.user.id in [member.id for member in interaction.channel.members]
        has_ticket_role = any(role.id == TICKET_ROLE_ID for role in interaction.user.roles)

        if not (is_ticket_creator or has_ticket_role):
            return await interaction.response.send_message(
                "You don't have permission to close this ticket.",
                ephemeral=True
            )

        # Send response first before locking
        embed = discord.Embed(
            description=f"This ticket has been locked by {interaction.user.mention}.",
            color=self.cog.bot.mode.EMBED_COLOR
        )
        embed.set_footer(text="Still need help? Create another ticket in the main channel.")
        await interaction.response.send_message(embed=embed)

        # Remove from active tickets and activity tracking
        user_id = None
        for uid, thread_id in self.cog.active_tickets.items():
            if thread_id == interaction.channel.id:
                user_id = uid
                break

        if user_id:
            del self.cog.active_tickets[user_id]

        if interaction.channel.id in self.cog.ticket_last_activity:
            del self.cog.ticket_last_activity[interaction.channel.id]

        # Only lock the thread (don't archive) so moderators can still see it
        await interaction.channel.edit(locked=True)

        # Update channel name to show it's resolved
        try:
            new_name = f"[Resolved] {interaction.channel.name}"
            await interaction.channel.edit(name=new_name)
        except:
            pass

async def setup(bot):
    await bot.add_cog(Ticket(bot))

    # Add persistent views
    bot.add_view(TicketPanelView(bot.get_cog('Ticket')))
    bot.add_view(TicketCloseView(bot.get_cog('Ticket')))